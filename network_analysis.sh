#!/bin/bash

# 网络分析脚本 - 统计服务器网络信息
# 作者: Augment Agent
# 日期: $(date +%Y-%m-%d)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 分隔线函数
print_separator() {
    echo -e "${BLUE}================================================================${NC}"
}

print_section() {
    echo -e "\n${GREEN}$1${NC}"
    print_separator
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo -e "${RED}警告: $1 命令未找到${NC}"
        return 1
    fi
    return 0
}

# 主函数
main() {
    echo -e "${CYAN}🔍 服务器网络信息分析报告${NC}"
    echo -e "${CYAN}服务器: $(hostname)${NC}"
    echo -e "${CYAN}时间: $(date)${NC}"
    print_separator

    # 1. 物理网络接口信息
    print_section "🔌 物理网络接口信息"
    if check_command "ip"; then
        echo -e "${YELLOW}所有网络接口:${NC}"
        ip addr show | grep -E "^[0-9]+:|inet " | while read line; do
            if [[ $line =~ ^[0-9]+: ]]; then
                interface=$(echo $line | cut -d: -f2 | tr -d ' ')
                state=$(echo $line | grep -o "state [A-Z]*" | cut -d' ' -f2)
                echo -e "  ${PURPLE}接口: $interface${NC} - 状态: $state"
            elif [[ $line =~ inet ]]; then
                ip=$(echo $line | awk '{print $2}')
                echo -e "    IP: $ip"
            fi
        done
        
        echo -e "\n${YELLOW}网络接口统计:${NC}"
        ip link show | grep -E "^[0-9]+:" | while read line; do
            interface=$(echo $line | cut -d: -f2 | tr -d ' ')
            if [[ $interface =~ ^(eno|enp|eth) ]]; then
                echo -e "  ${GREEN}物理网卡: $interface${NC}"
            elif [[ $interface =~ ^(docker|br-) ]]; then
                echo -e "  ${BLUE}Docker网桥: $interface${NC}"
            elif [[ $interface =~ ^veth ]]; then
                echo -e "  ${CYAN}虚拟网线: $interface${NC}"
            elif [[ $interface == "lo" ]]; then
                echo -e "  ${YELLOW}回环接口: $interface${NC}"
            fi
        done
    fi

    # 2. Docker网络信息
    print_section "🐳 Docker网络信息"
    if check_command "docker"; then
        echo -e "${YELLOW}Docker网络列表:${NC}"
        docker network ls --format "table {{.Name}}\t{{.Driver}}\t{{.Scope}}\t{{.ID}}" 2>/dev/null || echo "Docker服务未运行或权限不足"
        
        echo -e "\n${YELLOW}Docker网络详细信息:${NC}"
        for network in $(docker network ls --format "{{.Name}}" 2>/dev/null | grep -v "host\|none"); do
            echo -e "\n  ${PURPLE}网络: $network${NC}"
            docker network inspect $network 2>/dev/null | jq -r '
                .[0] | 
                "    网关: " + (.IPAM.Config[0].Gateway // "未配置") + 
                "\n    子网: " + (.IPAM.Config[0].Subnet // "未配置") +
                "\n    驱动: " + .Driver
            ' 2>/dev/null || echo "    无法获取详细信息"
        done
    else
        echo "Docker未安装或不可用"
    fi

    # 3. 容器网络映射
    print_section "📦 容器网络映射"
    if check_command "docker"; then
        echo -e "${YELLOW}运行中的容器:${NC}"
        if docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}" 2>/dev/null | grep -q "."; then
            docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}" 2>/dev/null
            
            echo -e "\n${YELLOW}容器网络详情:${NC}"
            for container in $(docker ps --format "{{.Names}}" 2>/dev/null); do
                echo -e "\n  ${GREEN}容器: $container${NC}"
                docker inspect $container 2>/dev/null | jq -r '
                    .[0].NetworkSettings.Networks | 
                    to_entries[] | 
                    "    网络: " + .key + 
                    "\n    IP地址: " + (.value.IPAddress // "未分配") +
                    "\n    网关: " + (.value.Gateway // "未配置") +
                    "\n    MAC地址: " + (.value.MacAddress // "未配置")
                ' 2>/dev/null || echo "    无法获取网络信息"
                
                # 获取端口映射
                ports=$(docker port $container 2>/dev/null)
                if [ ! -z "$ports" ]; then
                    echo -e "    ${CYAN}端口映射:${NC}"
                    echo "$ports" | sed 's/^/      /'
                fi
            done
        else
            echo "没有运行中的容器"
        fi
    fi

    # 4. 网桥信息
    print_section "🌉 网桥信息"
    if check_command "brctl"; then
        echo -e "${YELLOW}网桥列表:${NC}"
        brctl show 2>/dev/null || echo "brctl命令不可用，使用ip命令:"
    fi
    
    if check_command "ip"; then
        echo -e "${YELLOW}虚拟网桥接口:${NC}"
        ip link show type bridge 2>/dev/null | grep -E "^[0-9]+:" | while read line; do
            bridge=$(echo $line | cut -d: -f2 | tr -d ' ')
            echo -e "  ${BLUE}网桥: $bridge${NC}"
            ip addr show $bridge 2>/dev/null | grep "inet " | awk '{print "    IP: " $2}'
        done
    fi

    # 5. 路由信息
    print_section "🛣️  路由信息"
    if check_command "ip"; then
        echo -e "${YELLOW}路由表:${NC}"
        ip route show | head -20 | while read route; do
            echo "  $route"
        done
        
        echo -e "\n${YELLOW}Docker相关路由:${NC}"
        ip route show | grep -E "(docker|172\.|192\.168\.)" | while read route; do
            echo -e "  ${CYAN}$route${NC}"
        done
    fi

    # 6. 网络统计
    print_section "📊 网络统计"
    echo -e "${YELLOW}网络接口流量统计:${NC}"
    if [ -f /proc/net/dev ]; then
        echo -e "  ${PURPLE}接口名称\t\t接收字节\t\t发送字节${NC}"
        cat /proc/net/dev | grep -E "(eno|enp|eth|docker|br-)" | while read line; do
            interface=$(echo $line | cut -d: -f1 | tr -d ' ')
            rx_bytes=$(echo $line | awk '{print $2}')
            tx_bytes=$(echo $line | awk '{print $10}')
            if [ ! -z "$rx_bytes" ] && [ ! -z "$tx_bytes" ]; then
                printf "  %-15s\t%15s\t%15s\n" "$interface" "$rx_bytes" "$tx_bytes"
            fi
        done
    fi

    # 7. 网络连接
    print_section "🔗 网络连接"
    if check_command "ss"; then
        echo -e "${YELLOW}监听端口:${NC}"
        ss -tulpn | grep LISTEN | head -10 | while read line; do
            echo "  $line"
        done
    elif check_command "netstat"; then
        echo -e "${YELLOW}监听端口:${NC}"
        netstat -tulpn | grep LISTEN | head -10 | while read line; do
            echo "  $line"
        done
    fi

    # 8. 总结
    print_section "📋 总结"
    echo -e "${YELLOW}网络接口总数:${NC}"
    if check_command "ip"; then
        physical_count=$(ip link show | grep -c -E "^[0-9]+: (eno|enp|eth)")
        docker_count=$(ip link show | grep -c -E "^[0-9]+: (docker|br-)")
        veth_count=$(ip link show | grep -c -E "^[0-9]+: veth")
        
        echo -e "  物理网卡: ${GREEN}$physical_count${NC}"
        echo -e "  Docker网桥: ${BLUE}$docker_count${NC}"
        echo -e "  虚拟网线(veth): ${CYAN}$veth_count${NC}"
    fi
    
    if check_command "docker"; then
        container_count=$(docker ps -q 2>/dev/null | wc -l)
        network_count=$(docker network ls -q 2>/dev/null | wc -l)
        echo -e "  运行容器数: ${GREEN}$container_count${NC}"
        echo -e "  Docker网络数: ${BLUE}$network_count${NC}"
    fi

    print_separator
    echo -e "${GREEN}✅ 网络分析完成！${NC}"
}

# 执行主函数
main "$@"
